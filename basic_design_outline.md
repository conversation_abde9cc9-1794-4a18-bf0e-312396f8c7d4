# 各種基本設計書のアウトライン

## a. BCMonitoringアーキテクチャ概要

BCMonitoringアーキテクチャ概要には、以下の要素を記述する。

1. BCMonitoringを中心とした周辺のAWSアーキテクチャ概要図
2. BCMonitoringを実装するうえでの言語仕様、必要ライブラリなどのコンポーネント概要
3. パフォーマンス・スケーリング
   * BCMonitoringをスケーリングさせるうえでの前提条件（→水平スケールできる仕組みでないことをまとめておく必要がある）
4. Listenerノード接続管理
   * Listenerノードとの接続方式と必要接続数

## b. BCMonitoring実装方針

1. アプリケーションのメイン処理概要（sequenced diagram）：
   1. 処理概要
   2. 処理フロー（シーケンス図）

2. 非同期処理の概要設計書
   1. 処理概要
   2. 非同期処理が必要な理由
      1. イベント監視の連続性保証：
         1. pending transaction処理中も新規ブロック監視を継続する必要性
         2. ブロックチェーンは止まらない → 監視も止められない
   3. 処理フロー（非同期処理がわかるシーケンス図）

3. Web3j統合の設計書
   1. Web3jの概要
   2. 利用しているWeb3jメソッド一覧
   3. Web3jライブラリの使用方針
      1. リアルタイム監視: 新規ブロック通知の即座受信
         1. 概要
         2. 処理フロー（シーケンス図）
      2. データ取得: ブロック詳細・トランザクション情報の取得
         1. 概要
         2. 処理フロー（シーケンス図）
      3. イベント解析: スマートコントラクトイベントログの解析
         1. 概要
         2. 処理フロー（シーケンス図）
   4. 接続アーキテクチャ
      1. 接続方式の使い分け
      2. 接続管理設計
   5. ABI統合とイベント処理
      1. ABI統合アーキテクチャ
      2. イベントフィルタリング
         1. ABI定義による動的フィルタリング
            1. Web3jのエンコードメソッド（EventEncoder.encode()） によるシグネチャ生成
            2. 登録済みイベントのみ処理対象
            3. 未知イベントの除外
      3. イベントデコーディング
   6. エラーハンドリング
      * WebSocket切断
         * 自動再接続設定
      * HTTP RPC エラー
      * データレベルエラー

## c. テーブル設計

## d. BCMonitoring SpringBootプロジェクト構成

1. BCMonitoring 概要
2. リポジトリ構成方針
   1. Clean Architecture採用理由
   2. レイヤー分離の方針
3. 各ディレクトリの役割

## e. アプリケーション基本ログ指針

1. BCMonitoring関連アプリケーション基本ログ指針
   1. ログの使用方針
   2. 監視・アラート設計
      1. ブロック遅延のWARNが5回継続出る場合はアラートを流すなどアラートのパターン設計する
   3. ログ禁止事項
      1. 秘匿情報（秘密鍵等）に該当するものはログ出力しない

2. BCMonitoringのログ設計
   1. ログ出力先
      1. New Relic, Cloud Watch, Slackなどへ出力する
   2. ログフォーマット
   3. ログ出力項目（どのアクションがどのログに該当するか、ログレベルごとの出力情報定義）
      1. 使用ライブラリ
      2. fatalログ
      3. errorログ
      4. warningログ
      5. infoログ
      6. debugログ