# 基本設計書_Web3j統合

## 1. Web3jの概要

### 1.1 Web3jとは
Web3jは、Java/Android向けのEthereumブロックチェーン統合ライブラリです。BCMonitoringシステムでは、Ethereumブロックチェーンとの通信、イベント監視、データ取得の中核技術として使用されています。

### 1.2 BCMonitoringでの役割
- **リアルタイム監視**: WebSocket接続による新規ブロック通知の即座受信
- **データ取得**: WebSocket RPC接続によるブロック詳細・トランザクション情報の取得
- **イベント解析**: スマートコントラクトイベントログの解析とデコーディング

## 2. 利用しているWeb3jメソッド一覧

| メソッド | 用途 | 接続方式 | 実装クラス |
|---------|------|---------|-----------|
| `newHeadsNotifications()` | 新規ブロック通知購読 | WebSocket | EthEventLogDao |
| `ethGetBlockByNumber()` | ブロック詳細取得 | WebSocket RPC | EthEventLogDao |
| `ethGetLogs()` | 過去ブロックのイベントログ取得 | WebSocket RPC | EthEventLogDao |
| `Contract.staticExtractEventParameters()` | イベントパラメータ抽出 | - | EthEventLogDao |
| `EventEncoder.encode()` | イベントシグネチャ生成 | - | AbiParser |

## 3. Web3jライブラリの使用方針

### 3.1 リアルタイム監視: 新規ブロック通知の即座受信

#### 概要
WebSocket接続を使用してEthereumノードから新規ブロック通知をリアルタイムで受信し、含まれるイベントログを即座に処理します。

#### 処理フロー
```mermaid
sequenceDiagram
    participant BesuNode as BN
    participant W3J as Web3j WebSocket
    participant ELD as EthEventLogDao
    participant Q as BlockingQueue
    participant MS as MonitorEventService

    ELD->>W3J: newHeadsNotifications().subscribe()
    BesuNode->>W3J: 新規ブロック通知
    W3J->>ELD: onNext(NewHeadsNotification)
    ELD->>BesuNode: ethGetBlockByNumber() (WebSocket RPC)
    BesuNode->>ELD: ブロック詳細データ
    ELD->>ELD: イベントログ解析
    ELD->>Q: Transaction.put()
    Q->>MS: Transaction取得
    MS->>MS: DynamoDB保存処理
```

#### 実装詳細
- **接続管理**: `Web3jConfig.getWeb3j()`でWebSocket接続を取得
- **購読処理**: `web3j.newHeadsNotifications().subscribe()`でFlowable購読
- **非同期処理**: onNext/onError/onCompleteコールバックで非同期処理

### 3.2 データ取得: ブロック詳細・トランザクション情報の取得

#### 概要
WebSocket RPC接続を使用してブロック詳細、トランザクション情報、イベントログを取得します。リアルタイム監視と過去ブロック処理の両方で使用されます。

#### 処理フロー
```mermaid
sequenceDiagram
    participant ELD as EthEventLogDao
    participant W3JC as Web3jCaller
    participant BesuNode as BN

    ELD->>W3JC: ethGetBlockByNumber()
    W3JC->>BesuNode: WebSocket RPC Request
    BesuNode->>W3JC: Block Data
    W3JC->>ELD: EthBlock.Block

    ELD->>W3JC: ethGetLogs()
    W3JC->>BesuNode: WebSocket RPC Request
    BesuNode->>W3JC: Log Data
    W3JC->>ELD: List<EthLog>
```

#### 実装詳細
- **接続管理**: `Web3jConfig.getWeb3jCaller()`で専用WebSocket RPC接続を取得
- **ブロック取得**: `ethGetBlockByNumber(blockNumber, true)`でトランザクション詳細込み取得
- **ログ取得**: `ethGetLogs(filter)`で指定範囲のイベントログを取得

### 3.3 Web3jメソッド使用シーケンスフロー

#### 新規ブロック監視の全体フロー
```mermaid
sequenceDiagram
    participant BesuNode as BN
    participant Web3jWS as W3J_WS
    participant Web3jRPC as W3J_RPC
    participant EthEventLogDao as ELD
    participant AbiParser as AP
    participant BlockingQueue as Q

    Note over ELD: 購読開始
    ELD->>Web3jWS: newHeadsNotifications().subscribe()

    Note over BesuNode: 新規ブロック生成
    BesuNode->>Web3jWS: 新規ブロック通知
    Web3jWS->>ELD: onNext(NewHeadsNotification)

    Note over ELD: ブロック詳細取得
    ELD->>Web3jRPC: ethGetBlockByNumber(blockNumber, true)
    Web3jRPC->>BesuNode: WebSocket RPC Request
    BesuNode->>Web3jRPC: Block Data with Transactions
    Web3jRPC->>ELD: EthBlock.Block

    Note over ELD: トランザクション処理ループ
    loop 各トランザクション
        ELD->>Web3jRPC: ethGetTransactionReceipt(txHash)
        Web3jRPC->>BesuNode: WebSocket RPC Request
        BesuNode->>Web3jRPC: Transaction Receipt
        Web3jRPC->>ELD: TransactionReceipt

        loop 各ログ
            ELD->>AP: getABIEventByLog(log)
            AP->>ELD: Event定義 or null
            alt Event定義が存在
                ELD->>ELD: Contract.staticExtractEventParameters()
                ELD->>ELD: Event entity作成
            end
        end
    end

    ELD->>Q: Transaction.put()
```

#### 過去ブロック処理の全体フロー
```mermaid
sequenceDiagram
    participant BesuNode as BN
    participant Web3jRPC as W3J_RPC
    participant EthEventLogDao as ELD
    participant AbiParser as AP

    Note over ELD: 過去ブロックログ取得
    ELD->>Web3jRPC: ethGetLogs(filter)
    Web3jRPC->>BesuNode: WebSocket RPC Request
    BesuNode->>Web3jRPC: Log Data Array
    Web3jRPC->>ELD: List<EthLog.LogResult>

    Note over ELD: ブロックタイムスタンプ取得
    loop 各ブロック番号
        ELD->>Web3jRPC: ethGetBlockByNumber(blockNumber, false)
        Web3jRPC->>BesuNode: WebSocket RPC Request
        BesuNode->>Web3jRPC: Block Header Data
        Web3jRPC->>ELD: EthBlock.Block
    end

    Note over ELD: ログ処理
    loop 各ログ
        ELD->>AP: getABIEventByLog(log)
        AP->>ELD: Event定義 or null
        alt Event定義が存在
            ELD->>ELD: Contract.staticExtractEventParameters()
            ELD->>ELD: Event entity作成
        end
    end
```

## 4. 接続アーキテクチャ

### 4.1 接続方式の使い分け

| 接続方式 | 用途 | 特徴 | 実装 |
|---------|------|------|------|
| WebSocket | リアルタイム監視 | 双方向通信、プッシュ通知 | `web3j` (購読専用) |
| WebSocket RPC | データ取得 | 要求応答型、WebSocket上でRPC実行 | `web3jCaller` (API呼び出し専用) |

### 4.2 接続管理設計

#### 接続インスタンス管理
- **キャッシュ機能**: `Web3jConfig`で接続インスタンスをキャッシュ
- **遅延初期化**: 初回アクセス時に接続を作成
- **分離設計**: 購読用とAPI呼び出し用で別インスタンス

#### 接続設定
```java
// WebSocket接続設定
String wsEndpoint = String.format("ws://%s:%s", host, port);
WebSocketService webSocketService = new WebSocketService(wsEndpoint, true);
webSocketService.connect();
Web3j web3j = Web3j.build(webSocketService);
```

## 5. ABI統合とイベント処理

### 5.1 ABI統合アーキテクチャ

#### コンポーネント構成
- **AbiParser**: ABI JSON解析とイベント定義管理
- **ContractEvents**: コントラクト別イベント定義格納
- **ContractAbiEvent**: 個別イベント定義とメタデータ
- **contractEventStore**: イベント定義のメモリキャッシュ

#### データ構造
```java
// コントラクトアドレス -> イベント定義マップ
Map<String, ContractEvents> contractEventStore

// イベントシグネチャ -> イベント定義マップ  
Map<String, ContractAbiEvent> events
```

### 5.2 イベントフィルタリング

#### ABI定義による動的フィルタリング

**Web3jのエンコードメソッド（EventEncoder.encode()）によるシグネチャ生成**
```java
// イベント定義からシグネチャ生成
Event event = new Event(eventName, typeReferences);
String signature = EventEncoder.encode(event);
```

**登録済みイベントのみ処理対象**
- ABI定義に存在するイベントのみ処理
- 未知イベントは自動的に除外
- コントラクトアドレスとイベントシグネチャの両方で照合

**未知イベントの除外**
```java
public ContractAbiEvent getContractAbiEventByLog(Log log) throws Exception {
    String eventId = log.getTopics().get(0).toLowerCase();
    String logAddress = log.getAddress().toLowerCase();
    
    Map<String, ContractAbiEvent> events = 
        contractEventStore.containsKey(logAddress) 
            ? contractEventStore.get(logAddress).events 
            : Collections.emptyMap();
            
    return events.containsKey(eventId) ? events.get(eventId) : null;
}
```

### 5.3 イベントデコーディング

#### デコーディング処理フロー
1. **イベント定義取得**: ログのトピック[0]からイベントシグネチャを抽出
2. **ABI照合**: contractEventStoreからイベント定義を検索
3. **パラメータ抽出**: `Contract.staticExtractEventParameters()`でデコード
4. **値分離**: indexed/non-indexed値を分離
5. **JSON変換**: 構造化データをJSON形式でシリアライズ

#### 実装例
```java
// イベント値抽出
EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
List<Type> indexedParameters = eventValues.getIndexedValues();
List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();

// パラメータ名とのマッピング
Map<Boolean, List<AbiEventInput>> groupedInputs = 
    contractAbiEvent.getInputs().stream()
        .collect(Collectors.groupingBy(AbiEventInput::isIndexed));
```

## 6. エラーハンドリング

### 6.1 WebSocket切断

#### 自動再接続設定
- **エラー検知**: onErrorコールバックで接続エラーを検知
- **リソース解放**: `unsubscribe()` + `shutdownWeb3j()`で適切にクリーンアップ
- **再起動機能**: MonitoringRunnerConfigによる自動再起動

#### 実装詳細
```java
web3j.newHeadsNotifications().subscribe(
    // onNext: 正常処理
    newHeadsNotification -> { /* 処理 */ },
    
    // onError: 接続エラー時
    error -> {
        logger.error("Subscription error", error);
        unsubscribe();
        web3jConfig.shutdownWeb3j();
        // エラートランザクション生成（blockNumber=-1）
    },
    
    // onComplete: 購読完了時
    () -> logger.info("Subscription completed")
);
```

### 6.2 WebSocket RPC エラー

#### エラー分類と対応
- **接続エラー**: Web3jConnectionException発生、例外スロー
- **タイムアウト**: 設定値に基づく自動リトライ
- **データエラー**: ログ出力後、処理継続またはスキップ

### 6.3 データレベルエラー

#### エラーハンドリング戦略
- **ブロック処理エラー**: エラートランザクション生成（blockNumber=-1）
- **イベント解析エラー**: 該当ログをスキップ、処理継続
- **ABI定義不一致**: 未知イベントとして除外

#### エラー通知機能
```java
// エラー状態をキューに通知
transactions.put(
    Transaction.builder()
        .blockHeight(BlockHeight.builder().blockNumber(-1).build())
        .build()
);
```

## 7. パフォーマンス考慮事項

### 7.1 接続プール管理
- WebSocket接続: 1接続（購読専用）
- WebSocket RPC接続: 1接続（API呼び出し専用）
- 接続の使い分けによる負荷分散

### 7.2 メモリ管理
- BlockingQueue: Integer.MAX_VALUE（実質無制限）
- contractEventStore: メモリキャッシュによる高速アクセス
- イベントデータ: JSON形式でコンパクトに保存

### 7.3 処理効率
- 非同期処理: Flowableによる非ブロッキング処理
- バッチ処理: 過去ブロック処理での効率的なログ取得
- フィルタリング: ABI定義による事前フィルタリングで不要な処理を削減
